import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import '../baidu_index_logic.dart';

/// 账号管理对话框组件 - 完全按照原始设计实现
class AccountManagement extends StatelessWidget {
  AccountManagement({super.key});

  final logic = Get.find<BaiduIndexLogic>();
  final ScrollController _scrollController = ScrollController();

  void _showBatchEnableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('批量启用账号'),
          content: Text('确定要启用所有选中的账号吗？系统将检查每个账号是否满足启用条件。'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                // 获取所有选中的账号
                List<BaiDuUsers> selectedUsers = logic.getSelectedUsers();
                List<String> errorMessages = [];

                // 检查每个账号并启用
                for (var user in selectedUsers) {
                  if (user.isError) {
                    errorMessages.add("${user.username}: 账号request block");
                    continue;
                  }
                  if (user.cookie == null || user.cookie!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少Cookie");
                    continue;
                  }
                  if (user.apiKey == null || user.apiKey!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少API Key");
                    continue;
                  }
                  if (user.apiKeyTime == null || user.apiKeyTime!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少API Key时间");
                    continue;
                  }
                  if (user.username == "暂未登录") {
                    errorMessages.add("有账号未登录");
                    continue;
                  }

                  // 通过验证，启用账号
                  user.isStart = true;
                }

                // 更新UI
                logic.update(['list', 'two']);

                // 关闭对话框
                Navigator.of(context).pop();

                // 显示结果
                if (errorMessages.isEmpty) {
                  showToast("所有选中账号已成功启用");
                } else {
                  // 显示错误信息
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text('部分账号启用失败'),
                        content: Container(
                          width: 400,
                          height: 200,
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: errorMessages.map((msg) => Padding(
                                padding: EdgeInsets.only(bottom: 8),
                                child: Text(msg),
                              )).toList(),
                            ),
                          ),
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: Text('确定'),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      );
                    },
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
        id: "list",
        init: logic,
        builder: (logic) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: 750,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 头部区域 - 完全按照原始设计
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: TekSpacings().mainSpacing,
                    vertical: 12,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          // 全选复选框
                          Checkbox(
                            value: logic.isAllSelected,
                            onChanged: (value) => logic.toggleAllSelection(value ?? false),
                          ),
                          TekTypography(
                            text: "百度指数账号管理（${logic.users.length}）",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          // 批量操作按钮 - 完全按照原始设计
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "代理配置",
                            onPressed: logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showPzProxyDialog(context),
                          ),
                          SizedBox(width: 8),
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "批量设置代理",
                            onPressed: logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showBatchProxyDialog(context),
                          ),
                          SizedBox(width: 8),
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "批量启用",
                            onPressed: logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showBatchEnableDialog(context),
                          ),
                          SizedBox(width: 8),
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "添加账号",
                            onPressed: () {
                              logic.users.add(BaiDuUsers(
                                time_str: logic.formatDateTime(DateTime.now()),
                                username: "暂未登录"
                              ));
                              logic.update(['list', "two"]);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 账号列表 - 完全按照原始设计使用TekCard
                Flexible(
                  child: Scrollbar(
                    controller: _scrollController,
                    thumbVisibility: true,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Padding(
                        padding: EdgeInsets.all(TekSpacings().mainSpacing),
                        child: TekCard(
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              for(var i = 0; i < logic.users.length; i++)
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: logic.users[i].isError ? Colors.red.shade200 : Colors.transparent,
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: i < logic.users.length - 1 ? 1 : 0,
                                      ),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      // 添加复选框
                                      Checkbox(
                                        value: logic.users[i].isSelected,
                                        onChanged: (value) => logic.toggleUserSelection(i, value ?? false),
                                      ),
                                      // 账户信息
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              logic.users[i].username,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14,
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  logic.users[i].time_str,
                                                  style: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      // 开关控制 - 完全按照原始设计
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          _buildSwitch(
                                            "启用",
                                            logic.users[i].isStart,
                                            (value) {
                                              // 启用前检查必要信息
                                              if (value == true) {
                                                if (logic.users[i].cookie == null || logic.users[i].cookie!.isEmpty) {
                                                  showToast("请先登录获取Cookie");
                                                  return;
                                                }
                                                if (logic.users[i].apiKey == null || logic.users[i].apiKey!.isEmpty) {
                                                  showToast("API Key不能为空");
                                                  return;
                                                }
                                                if (logic.users[i].apiKeyTime == null || logic.users[i].apiKeyTime!.isEmpty) {
                                                  showToast("API Key时间不能为空");
                                                  return;
                                                }
                                                if (logic.users[i].username == "暂未登录") {
                                                  showToast("请先登录账号");
                                                  return;
                                                }
                                              }
                                              // 通过验证后更新状态
                                              logic.users[i].isStart = value;
                                              logic.update(['list','two']);
                                            },
                                          ),
                                          _buildSwitch(
                                            "代理",
                                            logic.users[i].isProxy,
                                            (value) {
                                              if (!logic.users[i].isStart) {
                                                if (value == true) {
                                                  if (logic.users[i].proxyAddress == null || logic.users[i].proxyAddress!.isEmpty) {
                                                    showToast("请先设置代理地址");
                                                    return;
                                                  }
                                                  if (logic.users[i].proxyPort == null || logic.users[i].proxyPort!.isEmpty) {
                                                    showToast("请先设置代理端口");
                                                    return;
                                                  }
                                                }
                                                logic.users[i].isProxy = value;
                                                logic.update(['list','two']);
                                              } else {
                                                showToast("请先停止账号再设置代理");
                                              }
                                            },
                                            enabled: !logic.users[i].isStart,
                                          ),
                                        ],
                                      ),
                                      // 添加代理倒计时显示 - 完全按照原始设计
                                      if (logic.users[i].isProxy &&
                                          logic.users[i].proxyStartTime != null &&
                                          logic.users[i].proxyValidTime != null)
                                        Container(
                                          margin: EdgeInsets.only(right: 8),
                                          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                          child: GetBuilder<BaiduIndexLogic>(
                                            id: 'proxy_timer_${i}',
                                            builder: (_) {
                                              DateTime now = DateTime.now();
                                              DateTime endTime = logic.users[i].proxyStartTime!.add(
                                                  Duration(minutes: logic.users[i].proxyValidTime!)
                                              );
                                              Duration remaining = endTime.difference(now);

                                              // 更新倒计时
                                              if (remaining.isNegative) {
                                                return Text(
                                                  "已过期",
                                                  style: TextStyle(
                                                    color: Colors.red,
                                                    fontSize: 12,
                                                  ),
                                                );
                                              }

                                              // 格式化剩余时间
                                              String remainingStr = remaining.inHours > 0
                                                  ? "${remaining.inHours}h${(remaining.inMinutes % 60)}m"
                                                  : "${remaining.inMinutes}m${(remaining.inSeconds % 60)}s";

                                              return Text(
                                                remainingStr,
                                                style: TextStyle(
                                                  color: Colors.blue,
                                                  fontSize: 12,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      // 操作按钮 - 完全按照原始设计
                                      SizedBox(width: 8),
                                      TekButton(
                                        size: TekButtonSize.small,
                                        type: TekButtonType.success,
                                        text: logic.users[i].username == "暂未登录" ? "登录" : "重新登录",
                                        onPressed: () => logic.loginAccount(logic.users[i]),
                                      ),
                                      SizedBox(width: 8),
                                      TekButton(
                                        size: TekButtonSize.small,
                                        type: TekButtonType.info,
                                        text: "代理设置",
                                        onPressed: () => _showProxyDialog(context, logic.users[i]),
                                      ),
                                      SizedBox(width: 8),
                                      TekButton(
                                        size: TekButtonSize.small,
                                        type: TekButtonType.danger,
                                        text: "删除",
                                        onPressed: () {
                                          logic.users.removeAt(i);
                                          logic.update(['list', "two"]);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
    );
  }

  /// 构建开关组件 - 完全按照原始设计
  Widget _buildSwitch(String label, bool value, Function(bool) onChanged, {bool enabled = true}) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 13),
          ),
          Switch(
            value: value,
            onChanged: enabled ? onChanged : null,
            activeColor: Colors.blue,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  /// 显示代理设置对话框 - 完全按照原始设计
  void _showProxyDialog(BuildContext context, BaiDuUsers user) {
    final formKey = GlobalKey<FormState>();
    final proxyController = TextEditingController(text: user.proxyAddress);
    final portController = TextEditingController(text: user.proxyPort);
    final proxyUsernameController = TextEditingController(text: user.proxyUsername);
    final proxyPasswordController = TextEditingController(text: user.proxyPassword);

    // 定义有效时间选项（分钟）
    final List<int> validTimeOptions = [5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    // 默认选择60分钟或最接近的现有设置
    int selectedMinutes = user.proxyValidTime ?? 60;

    TekDialogs.defaultDialog(
      context,
      width: 400,
      content: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TekTypography(
                text: "代理设置",
                type: TekTypographyType.headline,
              ),
              SizedBox(height: 16),

              // 代理基本信息
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: TekInput(
                      labelText: "代理地址",
                      controller: proxyController,
                      hintText: "例如: 127.0.0.1",
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入代理地址';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: TekInput(
                      labelText: "端口",
                      controller: portController,
                      hintText: "例如: 7890",
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入端口';
                        }
                        if (int.tryParse(value) == null) {
                          return '请输入有效端口';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理认证信息
              Row(
                children: [
                  Expanded(
                    child: TekInput(
                      labelText: "代理用户名",
                      controller: proxyUsernameController,
                      hintText: "可选",
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: TekInput(
                      labelText: "代理密码",
                      controller: proxyPasswordController,
                      hintText: "可选",
                      obscureText: true,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理有效时间下拉框
              GetBuilder<BaiduIndexLogic>(
                  id: "proxy_dialog",
                  init: logic,
                  builder: (logic) {
                    return Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "代理有效时间",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                              SizedBox(height: 4),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<int>(
                                    value: selectedMinutes,
                                    isExpanded: true,
                                    padding: EdgeInsets.symmetric(horizontal: 12),
                                    items: validTimeOptions.map((int minutes) {
                                      String displayText = minutes >= 60
                                          ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                          : "$minutes分钟";
                                      return DropdownMenuItem<int>(
                                        value: minutes,
                                        child: Text(displayText),
                                      );
                                    }).toList(),
                                    onChanged: (int? newValue) {
                                      if (newValue != null) {
                                        selectedMinutes = newValue;
                                        logic.update(['proxy_dialog']);
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),

              SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TekButton(
                    type: TekButtonType.danger,
                    text: "取消",
                    onPressed: () => Navigator.pop(context),
                  ),
                  SizedBox(width: 8),
                  TekButton(
                    type: TekButtonType.info,
                    text: "确认",
                    onPressed: () async {
                      if (formKey.currentState!.validate()) {
                        // 先检测代理是否可用
                        showToast("正在检测代理...");
                        bool isProxyValid = await logic.checkProxy(
                          proxyController.text,
                          portController.text,
                          proxyUsernameController.text,
                          proxyPasswordController.text,
                        );
                        dismissAllToast();

                        if (!isProxyValid) {
                          showToast("代理连接失败，请检查代理设置");
                          return;
                        }

                        // 代理可用，更新信息
                        user.proxyAddress = proxyController.text;
                        user.proxyPort = portController.text;
                        user.proxyUsername = proxyUsernameController.text;
                        user.proxyPassword = proxyPasswordController.text;
                        user.proxyValidTime = selectedMinutes;
                        user.proxyStartTime = DateTime.now();
                        user.isProxy = true;

                        logic.update(['list', 'proxy_dialog', 'now', 'three']);
                        Navigator.pop(context);
                        showToast("代理设置成功");
                        logic.addLog("✅ 账号:${user.username}---代理设置成功");
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示批量代理设置对话框 - 完全按照原始设计
  void _showBatchProxyDialog(BuildContext context) {
    final textController = TextEditingController();
    final List<int> validTimeOptions = [5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    int selectedMinutes = 60;

    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
        id: "proxy_dialog",
        builder: (logic) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TekTypography(
                  text: "批量导入代理",
                  type: TekTypographyType.headline,
                ),
                SizedBox(height: 16),

                // 文本输入区域
                TekInput(
                  labelText: "代理列表",
                  controller: textController,
                  maxLines: 10,
                  hintText: "每行一个代理，格式：IP:端口 或 IP:端口:用户名:密码",
                ),

                SizedBox(height: 12),

                // 添加有效期选择
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "代理有效时间",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<int>(
                          value: selectedMinutes,
                          isExpanded: true,
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          items: validTimeOptions.map((int minutes) {
                            String displayText = minutes >= 60
                                ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                : "$minutes分钟";
                            return DropdownMenuItem<int>(
                              value: minutes,
                              child: Text(displayText),
                            );
                          }).toList(),
                          onChanged: (int? newValue) {
                            if (newValue != null) {
                              selectedMinutes = newValue;
                              logic.update(['proxy_dialog']);
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TekButton(
                      type: TekButtonType.danger,
                      text: "取消",
                      onPressed: () => Navigator.pop(context),
                    ),
                    SizedBox(width: 8),
                    TekButton(
                      type: TekButtonType.info,
                      text: "确认导入",
                      onPressed: () async {
                        final lines = textController.text.split('\n')
                            .where((line) => line.trim().isNotEmpty);
                        final proxies = lines
                            .map((line) => ProxyConfig.fromString(line))
                            .where((proxy) => proxy != null)
                            .cast<ProxyConfig>()
                            .toList();
                        if (proxies.isEmpty) {
                          showToast("没有有效的代理配置");
                          return;
                        }
                        print("selectedMinutes ---${selectedMinutes}");
                        await logic.importProxies(proxies, selectedMinutes);
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
        }
      ),
    );
  }

  /// 显示代理配置对话框 - 完全按照原始设计
  void _showPzProxyDialog(BuildContext context) {
    final textController = TextEditingController();
    final List<int> validTimeOptions = [1,5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    final List<String> validProxyOptions = ["否",'是'];

    String validProxyitem = logic.isZDProxy ? "是" : "否";
    textController.text = logic.pt_proxy_url;
    int selectedMinutes = 30;

    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
          id: "pz_proxy_dialog",
          builder: (logic) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TekTypography(
                    text: "代理配置",
                    type: TekTypographyType.headline,
                  ),
                  SizedBox(height: 16),

                  // 文本输入区域
                  TekInput(
                    labelText: "代理链接",
                    controller: textController,
                    maxLines: 1,
                    hintText: "",
                  ),

                  SizedBox(height: 12),

                  // 代理自动切换选择
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理自动切换",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: validProxyitem,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validProxyOptions.map((String option) {
                              return DropdownMenuItem<String>(
                                value: option,
                                child: Text(option),
                              );
                            }).toList(),
                            onChanged: (newValue) {
                              if (newValue != null) {
                                validProxyitem = newValue;
                                logic.isZDProxy = (validProxyitem == "是");
                                logic.update(['pz_proxy_dialog']);
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 12),

                  // 代理有效时间选择
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理有效时间",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<int>(
                            value: selectedMinutes,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validTimeOptions.map((int minutes) {
                              String displayText = minutes >= 60
                                  ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                  : "$minutes分钟";
                              return DropdownMenuItem<int>(
                                value: minutes,
                                child: Text(displayText),
                              );
                            }).toList(),
                            onChanged: (int? newValue) {
                              if (newValue != null) {
                                selectedMinutes = newValue;
                                logic.update(['pz_proxy_dialog']);
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TekButton(
                        type: TekButtonType.danger,
                        text: "取消",
                        onPressed: () => Navigator.pop(context),
                      ),
                      SizedBox(width: 8),
                      TekButton(
                        type: TekButtonType.info,
                        text: "确定配置",
                        onPressed: () async {
                          logic.pt_proxy_url = textController.text;

                          if (logic.pt_proxy_url.isEmpty) {
                            showToast("没有有效的代理链接");
                            return;
                          }

                          try {
                            // 获取所有选中用户
                            final selectedUsers = logic.users.where((user) => user.isSelected);

                            // 并行处理所有请求
                            final List<ProxyConfig> allProxies = await Future.wait(
                              selectedUsers.map((user) async {
                                final response = await logic.httpClientUtil.get(
                                  url: logic.pt_proxy_url,
                                );

                                return response.toString()
                                    .split('\n')
                                    .map((line) => line.trim())
                                    .where((line) => line.isNotEmpty)
                                    .map(ProxyConfig.fromString)
                                    .whereType<ProxyConfig>()
                                    .toList();
                              }),
                            ).then((results) => results.expand((list) => list).toList());

                            if (allProxies.isEmpty) {
                              showToast("未获取到有效代理");
                              return;
                            }
                            print("selectedMinutes ---${selectedMinutes}");
                            await logic.importProxies(allProxies, selectedMinutes);
                            Navigator.pop(context);
                          } catch (e) {
                            showToast("代理获取失败: ${e.toString()}");
                          }
                        },
                      )
                    ],
                  ),
                ],
              ),
            );
          }
      ),
    );
  }
}
